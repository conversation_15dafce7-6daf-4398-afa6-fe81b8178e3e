@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

/* Base application styles */
* {
    box-sizing: border-box;
}

html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Desktop-specific styles */
body {
    background-color: theme('colors.background.primary');
    color: theme('colors.text.primary');
    overflow: hidden; /* Prevent body scroll in desktop app */
}

/* Scrollbar styling for desktop */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: theme('colors.background.secondary');
}

::-webkit-scrollbar-thumb {
    background: theme('colors.border.primary');
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: theme('colors.text.muted');
}

/* Focus styles for accessibility */
*:focus {
    outline: 2px solid theme('colors.primary.coral');
    outline-offset: 2px;
}

/* Button focus styles */
button:focus {
    outline: 2px solid theme('colors.primary.coral');
    outline-offset: 2px;
}

/* Input focus styles */
input:focus, textarea:focus, select:focus {
    outline: none;
    ring: 2px solid theme('colors.primary.coral');
}

/* Card hover transitions */
.card-hover {
    transition: all 0.2s ease-in-out;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: theme('boxShadow.card-hover');
}

/* Responsive grid utilities */
.grid-responsive {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

@media (min-width: 1024px) {
    .grid-responsive {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }
}

@media (min-width: 1280px) {
    .grid-responsive {
        grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
    }
}

/* Sidebar navigation styles */
.nav-icon {
    transition: all 0.2s ease-in-out;
}

.nav-icon:hover {
    transform: scale(1.05);
}

.nav-icon.active {
    background-color: theme('colors.primary.coral');
}

/* Status dot styles */
.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-dot.black { background-color: theme('colors.status.black'); }
.status-dot.yellow { background-color: theme('colors.status.yellow'); }
.status-dot.orange { background-color: theme('colors.status.orange'); }
.status-dot.red { background-color: theme('colors.status.red'); }
