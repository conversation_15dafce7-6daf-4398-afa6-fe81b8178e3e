{{-- Responsive Grid Component --}}
@props([
    'cols' => 'auto-fill',
    'minWidth' => '280px',
    'gap' => '1.5rem',
    'class' => ''
])

@php
$gridClasses = match($cols) {
    '1' => 'grid-cols-1',
    '2' => 'grid-cols-1 desktop:grid-cols-2',
    '3' => 'grid-cols-1 desktop:grid-cols-2 desktop-lg:grid-cols-3',
    '4' => 'grid-cols-1 desktop:grid-cols-2 desktop-lg:grid-cols-3 desktop-xl:grid-cols-4',
    '5' => 'grid-cols-1 desktop:grid-cols-2 desktop-lg:grid-cols-3 desktop-xl:grid-cols-4 desktop-2xl:grid-cols-5',
    '6' => 'grid-cols-1 desktop:grid-cols-2 desktop-lg:grid-cols-3 desktop-xl:grid-cols-4 desktop-2xl:grid-cols-6',
    'auto-fill' => 'grid-responsive',
    default => $cols
};

$gapClasses = match($gap) {
    '0.5rem' => 'gap-2',
    '1rem' => 'gap-4',
    '1.5rem' => 'gap-6',
    '2rem' => 'gap-8',
    '2.5rem' => 'gap-10',
    '3rem' => 'gap-12',
    default => 'gap-6'
};
@endphp

<div class="grid {{ $gridClasses }} {{ $gapClasses }} {{ $class }}" 
     @if($cols === 'auto-fill') style="grid-template-columns: repeat(auto-fill, minmax({{ $minWidth }}, 1fr));" @endif>
    {{ $slot }}
</div>