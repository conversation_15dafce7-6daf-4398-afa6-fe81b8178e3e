# Tal Manga Reader - Component System

## Overview

This document outlines the component system and design tokens used in the Tal Manga Reader desktop application.

## Design Tokens

### Color Palette

#### Primary Colors
- `primary-coral`: #FF6B6B - Main brand color, used for active states and primary actions
- `primary-orange`: #FF8E53 - Secondary brand color, used for hover states

#### Secondary Colors
- `secondary-teal`: #4ECDC4 - Accent color for secondary actions
- `secondary-yellow`: #FFE66D - Highlight color
- `secondary-green`: #4ECDC4 - Success states
- `secondary-navy`: #2C3E50 - Dark accent

#### Background Colors
- `background-primary`: #F8F9FA - Main application background
- `background-secondary`: #FDFDFC - Card and panel backgrounds
- `background-panel`: #F6F7FA - Right panel and secondary areas

#### Text Colors
- `text-primary`: #1B1B18 - Primary text color
- `text-secondary`: #2C3E50 - Secondary text color
- `text-muted`: #64748B - Muted text color
- `text-light`: #A3A3A3 - Light text color

#### Border Colors
- `border-primary`: #E3E3E0 - Primary border color
- `border-secondary`: #E5E7EB - Secondary border color

#### Status Colors
- `status-black`: #1F2937 - Default status
- `status-yellow`: #F59E0B - Warning status
- `status-orange`: #F97316 - Attention status
- `status-red`: #EF4444 - Error/danger status

### Typography

- **Font Family**: Instrument Sans (primary), system fonts (fallback)
- **Font Sizes**: xs (12px), sm (14px), base (16px), lg (18px), xl (20px), 2xl (24px), 3xl (30px)
- **Font Weights**: 400 (normal), 500 (medium), 600 (semibold), 700 (bold)

### Spacing

- Standard spacing scale: 0.5rem, 1rem, 1.5rem, 2rem, 2.5rem, 3rem
- Custom spacing: 18 (4.5rem), 20 (5rem), 22 (5.5rem), 84 (21rem), 88 (22rem)

### Border Radius

- `lg`: 8px - Standard card radius
- `xl`: 12px - Large component radius
- `2xl`: 16px - Extra large radius

## Layout System

### Base Layout Structure

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Window                        │
├──────┬─────────────────────────────────────────┬────────────┤
│      │                Top Bar                  │            │
│ Side │                (80px)                   │   Right    │
│ Nav  ├─────────────────────────────────────────┤   Panel    │
│(64px)│                                         │  (336px)   │
│      │            Main Content                 │            │
│      │                                         │            │
│      │                                         │            │
└──────┴─────────────────────────────────────────┴────────────┘
```

### Responsive Breakpoints

- `desktop`: 1024px - Standard desktop
- `desktop-lg`: 1280px - Large desktop (shows right panel)
- `desktop-xl`: 1536px - Extra large desktop
- `desktop-2xl`: 1920px - Ultra-wide desktop

## Component Library

### Layout Components

#### `<x-layouts.app>`
Base application layout with sidebar, topbar, main content, and right panel.

**Props:**
- `title`: Page title for browser/window
- `active-page`: Current active page for navigation
- `page-title`: Display title in topbar
- `search-placeholder`: Search input placeholder
- `show-search`: Boolean to show/hide search

#### `<x-layouts.app>` (component)
Internal layout component that structures the main application areas.

**Slots:**
- `sidebar`: Left navigation content
- `topbar`: Top header content
- `main`: Main content area
- `rightbar`: Right panel content

### Navigation Components

#### `<x-navigation.sidebar>`
Left navigation sidebar with app logo and navigation icons.

**Props:**
- `active-page`: Currently active page identifier

#### `<x-navigation.topbar>`
Top header bar with page title and search functionality.

**Props:**
- `title`: Page title to display
- `search-placeholder`: Search input placeholder text
- `show-search`: Boolean to show/hide search bar

**Slots:**
- `actions`: Additional header actions
- `view-toggle`: View mode toggle buttons
- `filter-toggle`: Filter toggle button

### UI Components

#### `<x-ui.grid>`
Responsive grid system for laying out content.

**Props:**
- `cols`: Number of columns or 'auto-fill' (default: 'auto-fill')
- `min-width`: Minimum column width for auto-fill (default: '280px')
- `gap`: Grid gap size (default: '1.5rem')
- `class`: Additional CSS classes

#### `<x-ui.card>`
Reusable card component with consistent styling.

**Props:**
- `hover`: Enable hover effects (default: false)
- `padding`: Padding class (default: 'p-6')
- `class`: Additional CSS classes

#### `<x-ui.button>`
Standardized button component with multiple variants.

**Props:**
- `variant`: Button style - 'primary', 'secondary', 'outline', 'ghost', 'danger'
- `size`: Button size - 'sm', 'md', 'lg', 'xl'
- `type`: Button type (default: 'button')
- `disabled`: Disabled state (default: false)
- `class`: Additional CSS classes

#### `<x-ui.status-dot>`
Small status indicator dot.

**Props:**
- `status`: Status color - 'black', 'yellow', 'orange', 'red', 'green', 'teal'
- `class`: Additional CSS classes

## Usage Examples

### Basic Page Layout

```blade
<x-layouts.app 
    title="Library - Tal Manga Reader"
    :active-page="'library'"
    :page-title="'My Library'"
    :search-placeholder="'Search your library...'"
>
    <x-ui.grid cols="auto-fill" min-width="320px">
        <x-ui.card hover="true">
            <h3>Manga Title</h3>
            <p>Description...</p>
        </x-ui.card>
    </x-ui.grid>
</x-layouts.app>
```

### Button Usage

```blade
<x-ui.button variant="primary" size="lg">
    Primary Action
</x-ui.button>

<x-ui.button variant="outline" size="md">
    Secondary Action
</x-ui.button>
```

### Grid Layout

```blade
<x-ui.grid cols="3" gap="2rem">
    <x-ui.card>Card 1</x-ui.card>
    <x-ui.card>Card 2</x-ui.card>
    <x-ui.card>Card 3</x-ui.card>
</x-ui.grid>
```

## Accessibility

- All interactive elements have proper focus states
- Keyboard navigation is supported throughout
- Color contrast meets WCAG AA standards
- Screen reader friendly markup
- Semantic HTML structure

## Performance

- CSS custom properties for dynamic theming
- Optimized animations and transitions
- Efficient grid layouts
- Minimal JavaScript footprint
- Desktop-optimized rendering