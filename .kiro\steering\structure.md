# Project Structure

## NativePHP Laravel Application Structure

### Core Application (`app/`)
- `app/Http/` - Controllers, middleware, requests, and resources (native app routes)
- `app/Models/` - Eloquent models for manga, chapters, extensions, etc.
- `app/Providers/` - Service providers including **NativeAppServiceProvider** (main entry point)
- `app/View/` - View composers and custom Blade components for native UI
- `app/Services/` - Business logic services (extension management, manga processing)
- `app/Extensions/` - PHP-based manga source extensions with standardized interfaces

### NativePHP Configuration (`config/`)
- `config/nativephp.php` - **PRIMARY CONFIG**: app version, app_id, updater, native features
- `config/database.php` - SQLite configuration for local storage
- `config/queue.php` - Local queue processing (no external services)
- Standard Laravel config files adapted for native execution

### Database (`database/`)
- `database/migrations/` - Schema migrations (created AFTER UI completion)
- `database/seeders/` - Development data seeders
- `database/factories/` - Model factories for testing
- `database/database.sqlite` - Main application SQLite database
- `database/nativephp.sqlite` - NativePHP framework database

### Native UI Assets (`resources/`)
- `resources/views/` - Blade templates for native desktop UI
- `resources/css/` - TailwindCSS for responsive desktop layouts
- `resources/js/` - JavaScript for native webview interactivity
- `resources/images/` - Optimized assets for desktop performance

### Routes (`routes/`)
- `routes/web.php` - Native app routes (not traditional web routes)
- `routes/console.php` - Artisan console commands for native features

### Testing (`tests/`)
- `tests/Feature/` - Feature tests for native app functionality
- `tests/Unit/` - Unit tests for components and services
- `tests/Pest.php` - Pest configuration for native testing

## UI-First Development Structure
**CRITICAL**: All UI must be implemented FIRST before any backend logic

### Desktop UI Pages (Pixel-Perfect Implementation Required)
- `resources/views/layouts/app.blade.php` - Main native app layout
- `resources/views/home.blade.php` - Dashboard with recent activities
- `resources/views/library.blade.php` - User's manga collection with filtering
- `resources/views/updates.blade.php` - Chapter updates with date grouping
- `resources/views/history.blade.php` - Reading history with pagination
- `resources/views/detail.blade.php` - Manga details with native dialogs
- `resources/views/reader.blade.php` - Full-screen manga reading interface
- `resources/views/downloads.blade.php` - Download management with progress
- `resources/views/extensions.blade.php` - Extension marketplace interface
- `resources/views/settings.blade.php` - Application settings and about

### Component Structure (Modular & Reusable)
- `resources/views/components/` - Reusable UI components
- `resources/views/components/manga-card.blade.php` - Manga display component
- `resources/views/components/chapter-list.blade.php` - Chapter listing component
- `resources/views/components/filter-panel.blade.php` - Filtering interface
- `resources/views/components/progress-bar.blade.php` - Download progress
- `resources/views/components/native-dialog.blade.php` - OS-native dialogs

## Extension System Architecture
- `app/Contracts/SourceInterface.php` - Standardized extension interface
- `app/Extensions/` - PHP extension implementations
- `app/Services/ExtensionManager.php` - Extension loading and management
- Data structures: `Manga`, `Chapter`, `Page`, `Comment` models

## Native Desktop Conventions
- **OS Design Guidelines**: Follow Windows Fluent, macOS Human Interface, Linux GNOME
- **Responsive Desktop**: Support all screen sizes (laptops to ultrawides)
- **Keyboard Navigation**: Full keyboard accessibility
- **Native Integration**: File dialogs, notifications, system tray
- **Performance**: Optimized for desktop hardware and instant resizing

## Development Workflow
1. **UI Phase**: Complete pixel-perfect UI for all screens and components
2. **Database Phase**: Create migrations for all entities after UI approval
3. **Logic Phase**: Implement core features from main to advanced
4. **Testing Phase**: Comprehensive testing across all desktop platforms

## File Generation Rules
- **ALWAYS use Artisan commands**: `php artisan make:*` for all file generation
- **NEVER edit package files directly**: Use `composer` and `npm` commands only
- **Follow Laravel conventions**: Proper naming and structure for all files
- **Modular approach**: Reusable, maintainable, and extensible code structure

## Asset Optimization
- **TailwindCSS**: Primary framework for responsive desktop layouts
- **Vite**: Hot reload support for native development
- **SVG Icons**: Scalable vector graphics for all resolutions
- **Image Optimization**: Efficient loading for manga content
- **Performance**: Minimize bundle size for faster native app startup