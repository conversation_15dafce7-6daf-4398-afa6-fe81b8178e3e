{{-- Reusable Button Component --}}
@props([
    'variant' => 'primary',
    'size' => 'md',
    'type' => 'button',
    'disabled' => false,
    'class' => ''
])

@php
$baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

$variantClasses = match($variant) {
    'primary' => 'bg-primary-coral text-white hover:bg-primary-orange focus:ring-primary-coral',
    'secondary' => 'bg-secondary-teal text-white hover:bg-secondary-green focus:ring-secondary-teal',
    'outline' => 'border border-border-primary text-text-primary hover:bg-background-panel focus:ring-primary-coral',
    'ghost' => 'text-text-primary hover:bg-background-panel focus:ring-primary-coral',
    'danger' => 'bg-status-red text-white hover:bg-red-600 focus:ring-status-red',
    default => 'bg-primary-coral text-white hover:bg-primary-orange focus:ring-primary-coral'
};

$sizeClasses = match($size) {
    'sm' => 'px-3 py-1.5 text-sm',
    'md' => 'px-4 py-2 text-sm',
    'lg' => 'px-6 py-3 text-base',
    'xl' => 'px-8 py-4 text-lg',
    default => 'px-4 py-2 text-sm'
};
@endphp

<button 
    type="{{ $type }}"
    @if($disabled) disabled @endif
    class="{{ $baseClasses }} {{ $variantClasses }} {{ $sizeClasses }} {{ $class }}"
    {{ $attributes }}
>
    {{ $slot }}
</button>