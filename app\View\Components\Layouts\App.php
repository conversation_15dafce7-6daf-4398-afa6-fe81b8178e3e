<?php

namespace App\View\Components\Layouts;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class App extends Component
{
    /**
     * The title of the page
     */
    public string $title;

    /**
     * The currently active page
     */
    public string $activePage;

    /**
     * The page title displayed in the header
     */
    public string $pageTitle;

    /**
     * The search placeholder text
     */
    public string $searchPlaceholder;

    /**
     * Whether to show the search bar
     */
    public bool $showSearch;

    /**
     * Create a new component instance.
     */
    public function __construct(
        string $title = 'Tal Manga Reader',
        string $activePage = 'dashboard',
        string $pageTitle = 'Dashboard',
        string $searchPlaceholder = 'Search...',
        bool $showSearch = true
    ) {
        $this->title = $title;
        $this->activePage = $activePage;
        $this->pageTitle = $pageTitle;
        $this->searchPlaceholder = $searchPlaceholder;
        $this->showSearch = $showSearch;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.layouts.app');
    }
}
