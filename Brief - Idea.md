

**App Specification: Tal maga reader - A Desktop Manga Reader**

**1. Overview**

This document outlines the requirements for Tal maga reader, a desktop manga/webtoon/comics reader application built using PHP (PHPNative and Pure PHP) and HTML + JS (Blade, TailwindCSS, and CSS whenver needed).  The application aims for a native desktop experience, prioritizing performance, responsiveness, and efficient data management. Tal maga reader will be entirely offline, except for fetching data from manga sources via extensions.

**2. Main Pages (Screens)**

*   Home (Dashboard)
*   Library
*   Update
*   History
*   Detail
*   Settings
*   Extensions
*   Downloads
*   Reader

**3. Page-Specific Features and Functionality**

*   **3.1 Home (Dashboard)**

    *   Displays recent activities.
    *   **Content:**
        *   5 Latest Updated Chapters
        *   5+ New Series
        *   Release Calendar: An in-app calendar predicting future manga release dates based on historical release data in the local database.
        *   Last Releases
        *   The Most Read
        *   Favorites
        *   Latest Comments
    *   Data Source: Local database.

*   **3.2 Library**

    *   Lists all series added by the user.
    *   **Filtering:**
        *   Conventional Filters
        *   Extension-Based Filters
        *   Bookmarks
        *   Date
        *   Custom Filters (Tags): User-created tags.
    *   Filter to display series releases on a specific date.

*   **3.3 Update**

    *   Lists updates, sorted and grouped by date.
    *   Pagination: Combines "load more" and date-based navigation.
    *   Chapters grouped by series and date.
    *   Newly added series appear *only after* a chapter update.

*   **3.4 History**

    *   Displays recently read chapters, grouped by date, then series.
    *   Pagination: Combines "load more" and date-based navigation.

*   **3.5 Detail**

    *   Displays manga details for offline and online data.
    *   Comment Scraping: Extensions provide a mechanism to scrape comments.

*   **3.6 Extensions**

    *   Lists active (installed) extensions.
    *   Extensions Marketplace: In-app area to search, browse, install, and manage extensions from user-added repositories.

*   **3.7 Downloads**

    *   Lists active/pending downloads (status, progress, date).
    *   Downloads History: Shows downloaded and *not deleted* chapters.
    *   Search functionality.
    *   Pagination.
    *   Folders: Downloads are organized by folders.

*   **3.8 Reader**

    *   Interface for reading manga, offline and online.
    *   Customization: Reader settings (reading mode, layout, colors, etc.).
    *   Commands:
        *   Next/Previous Chapter navigation.
        *   Zoom: `Ctrl + Mouse Wheel`
        *   Save Image: Right-click context menu.
*   **3.9 Settings**
    *   Page linked to add the application settings.
    *   At the bottom of the page, the version and short about.

**4. Technical Requirements and Architecture**

*   **Programming Language:** PHP (PHPNative and Pure PHP)
*   **UI Framework:** HTML + JS (Blade, TailwindCSS, and CSS whenver needed)
*   **Extensions:** PHP (PHPNative and Pure PHP)
*   **Database:** A lightweight and fast local SQLite database.
*   **Architecture:** Modular, multi-file, asynchronous, resource-efficient design.
*   **No Online Services (Initially):** Entirely offline, except for fetching data via extensions.

**5. Data Management**

*   Graceful State Management: Handle state changes smoothly and predictably.
*   Dynamic Resource Utilization: Adapt to available system resources and use them efficiently.

**6. Extension API and Marketplace**

*   **6.1 Extension API:**

    *   **Purpose:** A standardized way for PHP (PHPNative and Pure PHP) extensions to interact with Tal maga reader, providing manga data (metadata, chapters, pages, comments).
    *   **Design Philosophy:**
        *   Ease of Use
        *   Flexibility
        *   Extensibility
        *   Performance
        *   Asynchronous Operations: *Strongly encouraged/required*.
        *   Versioning
        *   Tachiyomi Inspiration: Study the Tachiyomi extension system (Kotlin) for design principles and best practices.
    *   **Core Components:**
        *   **`Source` Interface:** All extensions must implement this. Defines methods for:
            *   Identifying the extension (name, ID, version, author, description).
            *   Specifying the source URL and supported languages.
            *   Indicating capabilities (searching, latest updates, comments).
        *   **`Manga` Data Structure:** Represents a manga series (title, author, description, cover URL, status, genres, source ID, *optionally:* comments).
        *   **`Chapter` Data Structure:** Represents a chapter (number, title, scanlator, upload date, source ID).
        *   **`Page` Data Structure:** Represents a page (number, image URL).
        *   **`Comment` Data Structure (Optional):** Represents a user comment (username, text, timestamp).
        *   **Data Fetching Methods:**  Asynchronous methods within the `Source` interface for fetching data (popular manga, latest updates, search, manga details, chapter list, page list, *optionally:* comments).
        *   **Request/Response Model:** A clear model for data fetching requests and responses.
        *   **Error Handling:** A consistent way for extensions to report errors.
    *   **Comment Scraping:**
        *   The API *supports* comment scraping; extensions are responsible for parsing.
        *   A standardized `Comment` data structure is provided.

*   **6.2 Extension Marketplace:**

    *   **Repository-Based:** User-added repositories.
    *   **Repository Format:** A simple, stunning design, human-readable format describing available extensions (name, ID, version, author, description, languages, download URL, source URL, *optionally:* supported features).
    *   **In-App Marketplace Features:**
        *   Add/Remove Repositories.
        *   Browse Extensions.
        *   Search Extensions.
        *   Install/Uninstall Extensions (download and load compiled assembly).
        *   Update Extensions (check repositories for updates).
        *   Display Extension Information.

