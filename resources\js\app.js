/**
 * Tal Manga Reader - Main Application JavaScript
 * Desktop-first manga reader application
 */

// Application initialization
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Initialize the application
 */
function initializeApp() {
    console.log('Tal Manga Reader - Initializing...');
    
    // Initialize navigation
    initializeNavigation();
    
    // Initialize search functionality
    initializeSearch();
    
    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
    
    // Initialize responsive behavior
    initializeResponsive();
    
    console.log('Tal Manga Reader - Ready!');
}

/**
 * Navigation functionality
 */
function initializeNavigation() {
    // Handle navigation clicks
    document.querySelectorAll('.nav-icon').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const page = this.getAttribute('onclick')?.match(/navigateTo\('(.+)'\)/)?.[1];
            if (page) {
                navigateTo(page);
            }
        });
    });
}

/**
 * Navigate to a specific page
 * @param {string} page - Page identifier
 */
function navigateTo(page) {
    console.log('Navigating to:', page);
    
    // Update active navigation state
    updateActiveNavigation(page);
    
    // For now, just update the URL
    // In a real app, this would trigger route changes
    if (window.history && window.history.pushState) {
        window.history.pushState({page}, '', `/${page}`);
    }
    
    // Trigger page change event
    window.dispatchEvent(new CustomEvent('pageChange', {
        detail: { page }
    }));
}

/**
 * Update active navigation state
 * @param {string} activePage - Currently active page
 */
function updateActiveNavigation(activePage) {
    // Remove active state from all nav items
    document.querySelectorAll('.nav-icon').forEach(button => {
        button.classList.remove('active');
        const icon = button.querySelector('span');
        const indicator = button.querySelector('.absolute.left-1\\/2');
        
        if (icon) {
            icon.className = icon.className.replace(/bg-primary-coral/g, 'bg-text-muted');
        }
        if (indicator) {
            indicator.remove();
        }
    });
    
    // Add active state to current page
    const activeButton = document.querySelector(`[onclick*="${activePage}"]`);
    if (activeButton) {
        activeButton.classList.add('active');
        const icon = activeButton.querySelector('span');
        if (icon) {
            icon.className = icon.className.replace(/bg-text-muted/g, 'bg-primary-coral');
        }
        
        // Add active indicator
        const indicator = document.createElement('span');
        indicator.className = 'absolute left-1/2 -bottom-1 w-6 h-1 bg-primary-coral rounded-full transform -translate-x-1/2';
        activeButton.appendChild(indicator);
    }
}

/**
 * Search functionality
 */
function initializeSearch() {
    const searchInput = document.getElementById('global-search');
    if (!searchInput) return;
    
    let searchTimeout;
    
    searchInput.addEventListener('input', function(e) {
        clearTimeout(searchTimeout);
        const query = e.target.value.trim();
        
        // Debounce search to avoid too many requests
        searchTimeout = setTimeout(() => {
            if (query.length > 0) {
                performSearch(query);
            } else {
                clearSearchResults();
            }
        }, 300);
    });

    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const query = e.target.value.trim();
            if (query.length > 0) {
                performSearch(query);
            }
        }
        
        // Handle escape key
        if (e.key === 'Escape') {
            e.target.value = '';
            clearSearchResults();
            e.target.blur();
        }
    });
}

/**
 * Perform search operation
 * @param {string} query - Search query
 */
function performSearch(query) {
    console.log('Searching for:', query);
    
    // Show loading state
    showSearchLoading();
    
    // TODO: Implement actual search logic
    // This would typically make an API call or filter local data
    
    // Simulate search delay
    setTimeout(() => {
        hideSearchLoading();
        // TODO: Display search results
    }, 500);
}

/**
 * Clear search results
 */
function clearSearchResults() {
    console.log('Clearing search results');
    hideSearchLoading();
    // TODO: Clear search results display
}

/**
 * Show search loading state
 */
function showSearchLoading() {
    const searchInput = document.getElementById('global-search');
    if (searchInput) {
        searchInput.classList.add('animate-pulse');
    }
}

/**
 * Hide search loading state
 */
function hideSearchLoading() {
    const searchInput = document.getElementById('global-search');
    if (searchInput) {
        searchInput.classList.remove('animate-pulse');
    }
}

/**
 * Keyboard shortcuts
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K for search focus
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('global-search');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }
        
        // Escape to clear search
        if (e.key === 'Escape') {
            const searchInput = document.getElementById('global-search');
            if (searchInput && document.activeElement === searchInput) {
                searchInput.value = '';
                clearSearchResults();
                searchInput.blur();
            }
        }
        
        // Number keys for navigation (1-5)
        if (e.key >= '1' && e.key <= '5' && !e.ctrlKey && !e.metaKey && !e.altKey) {
            const pages = ['dashboard', 'bookmarks', 'library', 'history', 'updates'];
            const pageIndex = parseInt(e.key) - 1;
            if (pages[pageIndex] && document.activeElement.tagName !== 'INPUT') {
                e.preventDefault();
                navigateTo(pages[pageIndex]);
            }
        }
    });
}

/**
 * Responsive behavior
 */
function initializeResponsive() {
    // Handle window resize
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            handleResize();
        }, 100);
    });
    
    // Initial resize handling
    handleResize();
}

/**
 * Handle window resize
 */
function handleResize() {
    const width = window.innerWidth;
    
    // Update CSS custom properties for responsive behavior
    document.documentElement.style.setProperty('--window-width', `${width}px`);
    
    // Handle right panel visibility
    const rightPanel = document.querySelector('aside.w-84');
    if (rightPanel) {
        if (width < 1280) { // desktop-lg breakpoint
            rightPanel.classList.add('hidden');
        } else {
            rightPanel.classList.remove('hidden');
        }
    }
    
    console.log('Window resized to:', width);
}

/**
 * Utility functions
 */

/**
 * Debounce function
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @param {boolean} immediate - Execute immediately
 */
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

/**
 * Throttle function
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Export functions for use in other modules
window.TalMangaReader = {
    navigateTo,
    performSearch,
    clearSearchResults,
    updateActiveNavigation,
    debounce,
    throttle
};