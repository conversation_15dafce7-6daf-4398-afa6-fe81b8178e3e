<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title ?? 'Tal Manga Reader' }}</title>

    {{-- Fonts --}}
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600,700" rel="stylesheet" />

    {{-- Styles --}}
    @vite('resources/css/app.css')
    
    {{-- Additional page-specific styles --}}
    @stack('styles')
</head>
<body class="min-h-screen w-full flex antialiased">
    {{-- Main Application Layout --}}
    <x-layouts.app>
        {{-- Left Navigation Sidebar --}}
        <x-slot name="sidebar">
            <x-navigation.sidebar :active-page="$activePage ?? 'dashboard'" />
        </x-slot>

        {{-- Top Header Bar --}}
        <x-slot name="topbar">
            <x-navigation.topbar 
                :title="$pageTitle ?? 'My Dashboard'" 
                :search-placeholder="$searchPlaceholder ?? 'titles, authors, publishers...'"
                :show-search="$showSearch ?? true"
            />
        </x-slot>

        {{-- Main Content Area --}}
        <x-slot name="main">
            {{ $slot }}
        </x-slot>

        {{-- Right Panel (optional) --}}
        <x-slot name="rightbar">
            {{ $rightPanel ?? '' }}
        </x-slot>
    </x-layouts.app>

    {{-- Scripts --}}
    @vite('resources/js/app.js')
    
    {{-- Additional page-specific scripts --}}
    @stack('scripts')
</body>
</html>