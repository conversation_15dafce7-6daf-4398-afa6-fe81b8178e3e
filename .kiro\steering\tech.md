# Technology Stack

CRITICAL: NEVER edits package files (composer.json, package.json, etc.) directly; they must ALWAYS use the appropriate package manager commands (Composer, npm, etc.), wait for the terminal to finish, and process the responses before deciding to continue, rethink, or web search for the correct command/result and ALWAYS use up-to-date Artisan or official commands to generate files, instead of creating files directly.

## Core Framework
- **Backend**: Laravel 12.x (PHP 8.2+) with NativePHP
- **Desktop Runtime**: NativePHP with Electron (bundled PHP runtime)
- **Frontend**: Blade templates with TailwindCSS 4.x in native webview
- **Database**: SQLite for local-first data storage
- **Build Tool**: Vite 6.x for asset compilation and hot reload

## NativePHP Architecture
- **Runtime**: Statically compiled PHP runtime bundled with application
- **UI Rendering**: Native webview with access to OS APIs from PHP
- **Distribution**: Native desktop binaries (no web server required)
- **Platform Support**: Windows, macOS, Linux desktop applications
- **Service Provider**: `app/Providers/NativeAppServiceProvider.php` for native features

## Key Dependencies
- **NativePHP**: `nativephp/electron` for desktop app framework
- **Testing**: Pest PHP for unit and feature testing
- **Code Quality**: Laravel Pint for code formatting
- **Queue Processing**: Laravel queues for background tasks (local execution)
- **Native APIs**: File system, notifications, dialogs, system integration

## Development Commands

### Native Development
```bash
# Start native desktop app in development (PRIMARY)
php artisan native:serve

# Start with hot reload for assets
composer native:dev

# Build native app for production
php artisan native:build
```

### Traditional Development (for testing)
```bash
# Start web server (for debugging only)
composer dev

# Run tests
composer test
php artisan test

# Code formatting
./vendor/bin/pint
```

### Asset Management
```bash
# Build assets for production
npm run build

# Watch assets during development (with native app)
npm run dev
```

### Database & Artisan
```bash
# ALWAYS use Artisan commands for file generation
php artisan make:controller ControllerName
php artisan make:model ModelName
php artisan make:migration create_table_name
php artisan make:request RequestName

# Database operations
php artisan migrate
php artisan migrate:fresh --seed

# Cache management
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

## Development Paradigm Shift
- **Local-First**: App runs entirely on user's device, no server required
- **Native APIs**: Direct access to OS features (notifications, file dialogs, system tray)
- **Offline-First**: Full functionality without internet connection
- **Security Model**: All code ships to client, never store secrets in app
- **Distribution**: Build and distribute native installers, not web deployments
- **Testing Environment**: Always test in native app, not browser

## Architecture Principles
- **UI-First Development**: Complete pixel-perfect UI before any backend logic
- **Modular Design**: Reusable components and clean separation of concerns
- **Native Integration**: Leverage OS-specific features and design guidelines
- **Performance Optimization**: Desktop-class performance with efficient resource usage
- **Cross-Platform**: Support Windows, macOS, and Linux with consistent experience
- **Extension API**: Standardized PHP interface for manga source extensions
- **Responsive Desktop**: Adaptive layouts for all desktop screen sizes and resolutions