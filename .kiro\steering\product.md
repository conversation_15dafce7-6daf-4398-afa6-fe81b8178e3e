# Product Overview

**Tal maga reader** is a native desktop manga/webtoon/comics reader application built with NativePHP, designed for offline-first reading with extension support.

## Core Purpose
- True native desktop manga reading experience using NativePHP
- Entirely offline operation with local-first architecture
- Extensible PHP-based system supporting multiple manga sources
- Efficient local data management with SQLite
- Cross-platform desktop support (Windows, macOS, Linux)

## Key Features
- **Library Management**: Personal manga collection with filtering and tagging
- **Reading Interface**: Customizable reader with zoom, navigation, and native file operations
- **Extension System**: PHP-based extensions for fetching manga from various sources
- **Download Management**: Offline chapter downloads with native progress notifications
- **Update Tracking**: System-level notifications for chapter updates and history
- **Release Calendar**: Predictive release scheduling based on historical data
- **Native Integration**: OS-level file dialogs, notifications, and system tray support

## Native Desktop Experience
- **OS Integration**: Native file dialogs, system notifications, and desktop shortcuts
- **Offline-First**: Fully functional without internet connection
- **Performance**: Optimized for desktop hardware with efficient resource usage
- **Responsive Design**: Adaptive layouts for all desktop screen sizes and resolutions
- **Accessibility**: Full keyboard navigation and screen reader support
- **Multi-Window**: Support for multiple reader windows and dialogs

## Development Approach
- **UI-First**: Complete pixel-perfect UI implementation before backend logic
- **Modular Architecture**: Reusable components and maintainable code structure
- **OS Design Guidelines**: Following platform-specific design patterns
- **Agile Methodology**: Iterative development with continuous testing across platforms