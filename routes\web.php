<?php

use Illuminate\Support\Facades\Route;

// Dashboard (Home)
Route::get('/', function () {
    return view('dashboard');
})->name('dashboard');

// Library
Route::get('/library', function () {
    return view('library');
})->name('library');

// Bookmarks
Route::get('/bookmarks', function () {
    return view('bookmarks');
})->name('bookmarks');

// History
Route::get('/history', function () {
    return view('history');
})->name('history');

// Updates
Route::get('/updates', function () {
    return view('updates');
})->name('updates');

// Settings
Route::get('/settings', function () {
    return view('settings');
})->name('settings');

// Notifications
Route::get('/notifications', function () {
    return view('notifications');
})->name('notifications');
