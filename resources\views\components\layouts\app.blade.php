<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title }}</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased">
    {{-- Base Application Layout Component --}}
    <div class="min-h-screen bg-background-secondary flex flex-col">
        <div class="flex flex-1">
            {{-- Left Navigation Sidebar (64px width, fixed) --}}
            <aside class="w-16 flex-shrink-0">
                <x-navigation.sidebar :active-page="$activePage" />
            </aside>
            
            {{-- Main Content Area --}}
            <main class="flex-1 flex flex-col min-w-0">
                {{-- Page Header (consistent across all pages) --}}
                <header class="h-20 flex-shrink-0 bg-white border-b border-border-primary px-6 flex items-center justify-between">
                    <h1 class="text-2xl font-semibold text-text-primary">{{ $pageTitle }}</h1>
                    
                    @if($showSearch)
                        <div class="flex-1 max-w-md ml-8">
                            <div class="relative">
                                <input 
                                    type="text" 
                                    placeholder="{{ $searchPlaceholder }}"
                                    class="w-full px-4 py-2 pl-10 pr-4 text-sm border border-border-secondary rounded-xl bg-background-primary focus:outline-none focus:ring-2 focus:ring-primary-coral focus:border-transparent transition-all duration-200"
                                >
                                <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    @endif
                </header>
                
                {{-- Content Section with Right Panel --}}
                <section class="flex-1 flex overflow-hidden">
                    {{-- Main Content Area (responsive) --}}
                    <div class="flex-1 overflow-y-auto bg-background-primary">
                        <div class="p-6">
                            {{ $slot }}
                        </div>
                    </div>
                    
                    {{-- Right Panel (calendar-style widget area) --}}
                    @isset($rightPanel)
                        <aside class="w-84 bg-background-panel border-l border-border-primary hidden desktop-lg:block flex-shrink-0">
                            <div class="p-6">
                                {{ $rightPanel }}
                            </div>
                        </aside>
                    @endisset
                </section>
            </main>
        </div>
    </div>
</body>
</html>