{{-- Top Header Bar Component --}}
@props([
    'title' => 'My Dashboard',
    'searchPlaceholder' => 'titles, authors, publishers...',
    'showSearch' => true
])

<div class="flex items-center justify-between h-full px-6">
    {{-- Page Title --}}
    <div class="flex items-center">
        <h1 class="text-2xl font-bold text-text-primary">{{ $title }}</h1>
        
        {{-- Optional breadcrumb or subtitle --}}
        @if(isset($subtitle))
            <span class="ml-3 text-text-muted">{{ $subtitle }}</span>
        @endif
    </div>

    {{-- Right Section --}}
    <div class="flex items-center gap-4">
        {{-- Search Bar (conditional) --}}
        @if($showSearch)
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-text-light" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                </div>
                <input 
                    type="text" 
                    placeholder="{{ $searchPlaceholder }}" 
                    class="w-80 pl-10 pr-4 py-2 text-sm bg-white border border-border-primary rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-coral focus:border-transparent placeholder:text-text-light transition-all duration-200"
                    id="global-search"
                />
            </div>
        @endif

        {{-- Additional header actions --}}
        @if(isset($actions))
            <div class="flex items-center gap-2">
                {{ $actions }}
            </div>
        @endif

        {{-- View Toggle (for pages that support it) --}}
        @if(isset($viewToggle))
            <div class="flex items-center bg-background-panel rounded-lg p-1">
                {{ $viewToggle }}
            </div>
        @endif

        {{-- Filter Toggle (for pages that support it) --}}
        @if(isset($filterToggle))
            <button class="p-2 text-text-muted hover:text-text-primary hover:bg-background-panel rounded-lg transition-all duration-200" 
                    title="Toggle Filters">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"/>
                </svg>
            </button>
        @endif
    </div>
</div>

{{-- Search functionality script --}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('global-search');
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);
            const query = e.target.value.trim();
            
            // Debounce search to avoid too many requests
            searchTimeout = setTimeout(() => {
                if (query.length > 0) {
                    performSearch(query);
                } else {
                    clearSearchResults();
                }
            }, 300);
        });

        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const query = e.target.value.trim();
                if (query.length > 0) {
                    performSearch(query);
                }
            }
        });
    }
});

function performSearch(query) {
    // This will be implemented when search functionality is added
    console.log('Searching for:', query);
    // TODO: Implement actual search logic
}

function clearSearchResults() {
    // Clear any search results or filters
    console.log('Clearing search results');
}
</script>