/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./resources/**/*.blade.php",
    "./resources/**/*.js",
    "./resources/**/*.vue",
    "./app/View/Components/**/*.php",
    "./storage/framework/views/*.php",
  ],
  theme: {
    extend: {
      colors: {
        // Primary color palette based on design requirements
        primary: {
          coral: '#FF6B6B',
          orange: '#FF8E53',
        },
        secondary: {
          teal: '#4ECDC4',
          yellow: '#FFE66D',
          green: '#4ECDC4',
          navy: '#2C3E50',
        },
        background: {
          primary: '#F8F9FA',
          secondary: '#FDFDFC',
          panel: '#F6F7FA',
        },
        text: {
          primary: '#1B1B18',
          secondary: '#2C3E50',
          muted: '#64748B',
          light: '#A3A3A3',
        },
        border: {
          primary: '#E3E3E0',
          secondary: '#E5E7EB',
        },
        status: {
          black: '#1F2937',
          yellow: '#F59E0B',
          orange: '#F97316',
          red: '#EF4444',
        }
      },
      fontFamily: {
        sans: ['Instrument Sans', 'ui-sans-serif', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],     // 12px
        'sm': ['0.875rem', { lineHeight: '1.25rem' }], // 14px
        'base': ['1rem', { lineHeight: '1.5rem' }],    // 16px
        'lg': ['1.125rem', { lineHeight: '1.75rem' }], // 18px
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],  // 20px
        '2xl': ['1.5rem', { lineHeight: '2rem' }],     // 24px
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }], // 30px
      },
      spacing: {
        '18': '4.5rem',   // 72px
        '20': '5rem',     // 80px
        '22': '5.5rem',   // 88px
        '84': '21rem',    // 336px
        '88': '22rem',    // 352px
      },
      borderRadius: {
        'lg': '0.5rem',   // 8px
        'xl': '0.75rem',  // 12px
        '2xl': '1rem',    // 16px
      },
      boxShadow: {
        'card': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'card-hover': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'panel': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
      animation: {
        'fade-in': 'fadeIn 0.2s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
      },
      screens: {
        'desktop': '1024px',
        'desktop-lg': '1280px',
        'desktop-xl': '1536px',
        'desktop-2xl': '1920px',
      },
    },
  },
  plugins: [],
}