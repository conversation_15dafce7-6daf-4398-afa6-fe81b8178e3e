{{-- Left Navigation Sidebar Component (64px width, fixed) --}}
@props(['activePage' => 'dashboard'])

<nav class="h-full flex flex-col items-center py-6 bg-white border-r border-border-primary">
    {{-- App Logo/Icon (coral/orange theme) --}}
    <div class="mb-8">
        <div class="w-10 h-10 bg-gradient-to-br from-primary-coral to-primary-orange rounded-xl flex items-center justify-center shadow-card hover:shadow-card-hover transition-all duration-200">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
            </svg>
        </div>
    </div>

    {{-- Navigation Icons (24x24px icons, vertically spaced) --}}
    <div class="flex flex-col gap-4 flex-1">
        {{-- Dashboard --}}
        <button class="nav-icon relative group {{ $activePage === 'dashboard' ? 'active' : '' }}" 
                onclick="navigateTo('dashboard')" 
                title="Dashboard"
                aria-label="Dashboard">
            <span class="block w-10 h-10 {{ $activePage === 'dashboard' ? 'bg-primary-coral text-white' : 'bg-transparent text-text-muted hover:bg-background-panel hover:text-text-primary' }} rounded-xl flex items-center justify-center transition-all duration-200 group-hover:scale-105">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                </svg>
            </span>
            @if($activePage === 'dashboard')
                <span class="absolute -right-1 top-1/2 w-1 h-6 bg-primary-coral rounded-full transform -translate-y-1/2"></span>
            @endif
        </button>

        {{-- Bookmarks --}}
        <button class="nav-icon relative group {{ $activePage === 'bookmarks' ? 'active' : '' }}" 
                onclick="navigateTo('bookmarks')" 
                title="Bookmarks"
                aria-label="Bookmarks">
            <span class="block w-10 h-10 {{ $activePage === 'bookmarks' ? 'bg-primary-coral text-white' : 'bg-transparent text-text-muted hover:bg-background-panel hover:text-text-primary' }} rounded-xl flex items-center justify-center transition-all duration-200 group-hover:scale-105">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17 3H7c-1.1 0-1.99.9-1.99 2L5 21l7-3 7 3V5c0-1.1-.9-2-2-2z"/>
                </svg>
            </span>
            @if($activePage === 'bookmarks')
                <span class="absolute -right-1 top-1/2 w-1 h-6 bg-primary-coral rounded-full transform -translate-y-1/2"></span>
            @endif
        </button>

        {{-- Library --}}
        <button class="nav-icon relative group {{ $activePage === 'library' ? 'active' : '' }}" 
                onclick="navigateTo('library')" 
                title="Library"
                aria-label="Library">
            <span class="block w-10 h-10 {{ $activePage === 'library' ? 'bg-primary-coral text-white' : 'bg-transparent text-text-muted hover:bg-background-panel hover:text-text-primary' }} rounded-xl flex items-center justify-center transition-all duration-200 group-hover:scale-105">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"/>
                </svg>
            </span>
            @if($activePage === 'library')
                <span class="absolute -right-1 top-1/2 w-1 h-6 bg-primary-coral rounded-full transform -translate-y-1/2"></span>
            @endif
        </button>

        {{-- History --}}
        <button class="nav-icon relative group {{ $activePage === 'history' ? 'active' : '' }}" 
                onclick="navigateTo('history')" 
                title="History"
                aria-label="History">
            <span class="block w-10 h-10 {{ $activePage === 'history' ? 'bg-primary-coral text-white' : 'bg-transparent text-text-muted hover:bg-background-panel hover:text-text-primary' }} rounded-xl flex items-center justify-center transition-all duration-200 group-hover:scale-105">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"/>
                </svg>
            </span>
            @if($activePage === 'history')
                <span class="absolute -right-1 top-1/2 w-1 h-6 bg-primary-coral rounded-full transform -translate-y-1/2"></span>
            @endif
        </button>
    </div>

    {{-- Bottom Navigation (Notifications and Settings) --}}
    <div class="flex flex-col gap-4 mt-auto">
        {{-- Notifications --}}
        <button class="nav-icon relative group {{ $activePage === 'notifications' ? 'active' : '' }}" 
                onclick="navigateTo('notifications')" 
                title="Notifications"
                aria-label="Notifications">
            <span class="block w-10 h-10 {{ $activePage === 'notifications' ? 'bg-primary-coral text-white' : 'bg-transparent text-text-muted hover:bg-background-panel hover:text-text-primary' }} rounded-xl flex items-center justify-center transition-all duration-200 group-hover:scale-105">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
                </svg>
            </span>
            {{-- Notification indicator --}}
            <span class="absolute top-1 right-1 w-2.5 h-2.5 bg-status-red rounded-full border-2 border-white"></span>
            @if($activePage === 'notifications')
                <span class="absolute -right-1 top-1/2 w-1 h-6 bg-primary-coral rounded-full transform -translate-y-1/2"></span>
            @endif
        </button>

        {{-- Settings --}}
        <button class="nav-icon relative group {{ $activePage === 'settings' ? 'active' : '' }}" 
                onclick="navigateTo('settings')" 
                title="Settings"
                aria-label="Settings">
            <span class="block w-10 h-10 {{ $activePage === 'settings' ? 'bg-primary-coral text-white' : 'bg-transparent text-text-muted hover:bg-background-panel hover:text-text-primary' }} rounded-xl flex items-center justify-center transition-all duration-200 group-hover:scale-105">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                </svg>
            </span>
            @if($activePage === 'settings')
                <span class="absolute -right-1 top-1/2 w-1 h-6 bg-primary-coral rounded-full transform -translate-y-1/2"></span>
            @endif
        </button>
    </div>
</nav>

<style>
/* Custom styles for navigation icons */
.nav-icon:focus {
    outline: 2px solid #FF6B6B;
    outline-offset: 2px;
}

.nav-icon:focus-visible {
    outline: 2px solid #FF6B6B;
    outline-offset: 2px;
}
</style>

<script>
function navigateTo(page) {
    // This will be implemented when routing is set up
    console.log('Navigate to:', page);
    // For now, just update the URL or trigger a page change
    window.location.href = '/' + page;
}

// Keyboard navigation support
document.addEventListener('DOMContentLoaded', function() {
    const navButtons = document.querySelectorAll('.nav-icon');
    
    navButtons.forEach((button, index) => {
        button.addEventListener('keydown', function(e) {
            let targetIndex;
            
            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    targetIndex = (index + 1) % navButtons.length;
                    navButtons[targetIndex].focus();
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    targetIndex = (index - 1 + navButtons.length) % navButtons.length;
                    navButtons[targetIndex].focus();
                    break;
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    button.click();
                    break;
            }
        });
    });
});
</script>